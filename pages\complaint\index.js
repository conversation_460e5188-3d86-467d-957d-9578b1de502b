// pages/complaint/index.js
import complaintApi from '../../api/modules/complaint';
import Session from '../../common/Session';


Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    // 表单数据
    category: 'suggestion', // 固定为建议
    subCategory: '', // 小类：platform/service/workflow
    title: '',
    content: '',
    contactInfo: '',
    photoList: [], // 图片列表

    // 建议分类选项
    subCategoryOptions: [
      { value: 'platform', label: '平台建议', desc: '对平台功能、体验的改进建议' },
      { value: 'service', label: '服务建议', desc: '对服务内容、流程的改进建议' },
      { value: 'workflow', label: '工作流程建议', desc: '对工作流程、操作体验的改进建议' }
    ],

    // UI状态
    canSubmit: false,
    isSubmitting: false,

    // 编辑模式
    isEditMode: false,
    editSuggestionId: '',

    // 验证状态
    contactError: '',

    // 字符计数
    titleMaxLength: 200,
    contentMaxLength: 2000,

    // 提示文本
    placeholders: {
      platform: '请分享您对平台功能、体验的改进建议，帮助我们提供更好的服务',
      service: '请分享您对服务内容、流程的改进建议，我们会认真考虑并优化',
      workflow: '请分享您对工作流程、操作体验的改进建议，帮助我们优化工作效率'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const userInfo = Session.getUser();
    this.setData({ userInfo });

    // 检查是否为编辑模式
    if (options.mode === 'edit' && options.id) {
      this.setData({
        isEditMode: true,
        editSuggestionId: options.id
      });
      this.loadSuggestionForEdit(options.id);
    } else {
      // 预设联系方式
      if (userInfo && userInfo.phone) {
        this.setData({ contactInfo: userInfo.phone });
      }
    }
  },

  /**
   * 加载建议数据进行编辑
   */
  async loadSuggestionForEdit(suggestionId) {
    try {
      wx.showLoading({ title: '加载中...' });

      const { userInfo } = this.data;
      const suggestionDetail = await complaintApi.detail(userInfo.id, suggestionId);

      if (suggestionDetail) {
        // 填充表单数据
        this.setData({
          subCategory: suggestionDetail.subCategory,
          title: suggestionDetail.title,
          content: suggestionDetail.content,
          contactInfo: suggestionDetail.contactInfo || '',
          photoList: suggestionDetail.photoURLs || []
        });

        // 验证联系方式和检查提交条件
        this.validateContact();
        this.checkCanSubmit();
      } else {
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('加载建议失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 选择建议类型
   */
  selectSubCategory(e) {
    const subCategory = e.currentTarget.dataset.subcategory;
    this.setData({ subCategory }, () => {
      this.checkCanSubmit();
    });
  },

  /**
   * 标题输入
   */
  onTitleInput(e) {
    this.setData({ title: e.detail.value }, () => {
      this.checkCanSubmit();
    });
  },

  /**
   * 内容输入
   */
  onContentInput(e) {
    this.setData({ content: e.detail.value }, () => {
      this.checkCanSubmit();
    });
  },

  /**
   * 联系方式输入
   */
  onContactInput(e) {
    const contactInfo = e.detail.value;
    this.setData({ contactInfo }, () => {
      this.validateContact();
      this.checkCanSubmit();
    });
  },

  /**
   * 验证联系方式
   */
  validateContact() {
    const { contactInfo } = this.data;
    let contactError = '';

    // 建议可以不提供联系方式，但如果提供了需要验证格式
    if (contactInfo.trim() && !this.isValidPhone(contactInfo.trim())) {
      contactError = '请输入正确的手机号码';
    }

    this.setData({ contactError });
    return !contactError;
  },

  /**
   * 验证手机号格式
   */
  isValidPhone(phone) {
    return /^1[3456789]\d{9}$/.test(phone);
  },

  /**
   * 检查是否可以提交
   */
  checkCanSubmit() {
    const { subCategory, title, content, contactError } = this.data;

    // 基本字段检查：建议类型、标题、内容必填，联系方式格式正确
    let canSubmit = subCategory && title.trim() && content.trim() && !contactError;

    this.setData({ canSubmit });
  },



  /**
   * 选择图片
   */
  chooseImage() {
    const { photoList, userInfo } = this.data;
    const remainCount = 6 - photoList.length;
    // 使用现有的上传功能
    this.uploadImage(
      this,
      '', // 存储字段
      `suggestion/${userInfo.id}-${userInfo.nickName}/`, // 上传key前缀
      remainCount // 最大数量
    ).then(res => {
      this.setData({
        photoList: [...photoList, ...res],
      });
    });
  },

  /**
   * 删除图片
   */
  deletePhoto(e) {
    const index = e.currentTarget.dataset.index;
    const photoList = [...this.data.photoList];
    photoList.splice(index, 1);
    this.setData({ photoList });
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      current: url,
      urls: this.data.photoList
    });
  },

  /**
   * 查看历史记录
   */
  viewHistory() {
    wx.navigateTo({
      url: '/pages/complaint/list'
    });
  },

  /**
   * 提交建议反馈
   */
  async submitSuggestion() {
    const {
      userInfo, category, subCategory, title, content, contactInfo,
      photoList, canSubmit, isEditMode, editSuggestionId
    } = this.data;

    if (!canSubmit) {
      wx.showToast({
        title: '请完善信息',
        icon: 'none'
      });
      return;
    }

    this.setData({ isSubmitting: true });

    try {
      const suggestionData = {
        category,
        subCategory,
        title: title.trim(),
        content: content.trim(),
        contactInfo: contactInfo.trim(),
        photoURLs: photoList,
        employeeId: userInfo.id // 自动传入当前员工ID
      };

      let res;
      if (isEditMode) {
        // 编辑模式：更新建议
        res = await complaintApi.update(userInfo.id, editSuggestionId, suggestionData);
      } else {
        // 创建模式：新建建议
        res = await complaintApi.create(userInfo.id, suggestionData);
      }

      if (!res) {
        wx.showToast({
          title: `${isEditMode ? '更新' : '提交'}失败，请稍后重试`,
          icon: 'none'
        });
        return;
      }

      wx.showToast({
        title: `${isEditMode ? '更新' : '提交'}成功`,
        icon: 'success'
      });

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } catch (error) {
      console.error(`${isEditMode ? '更新' : '提交'}建议失败:`, error);
      wx.showToast({
        title: `${isEditMode ? '更新' : '提交'}失败，请重试`,
        icon: 'none'
      });
    } finally {
      this.setData({ isSubmitting: false });
    }
  }
});
